'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { getCookie } from 'cookies-next';
import { appTheme as settingsTheme } from '@/app/theme';
import SettingsLayout from '../components/SettingsLayout';
import { Plus, Search, Loader2, AlertCircle, Users, Trash, X, ChevronDown } from 'lucide-react';

const AdminContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xl};
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: ${settingsTheme.spacing.xl};
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  box-shadow: ${settingsTheme.shadows.md};

  @media (min-width: ${settingsTheme.breakpoints['2xl']}) {
    max-width: 1400px;
  }

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.base};
    gap: ${settingsTheme.spacing.base};
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: ${settingsTheme.spacing.md};
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.md};
`;

const Title = styled.h1`
  font-size: ${settingsTheme.typography.fontSizes['2xl']};
  font-weight: ${settingsTheme.typography.fontWeights.bold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};
`;

const FilterContainer = styled.div`
  display: flex;
  gap: ${settingsTheme.spacing.md};
  align-items: center;
  flex-wrap: wrap;
`;

const OrganizationSelect = styled.div`
  position: relative;
  min-width: 250px;
`;

const SelectButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.background.light};
  border: 1px solid ${settingsTheme.colors.border};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.primary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.background.lighter};
    border-color: ${settingsTheme.colors.primary};
  }

  &:focus {
    outline: none;
    border-color: ${settingsTheme.colors.primary};
    box-shadow: 0 0 0 3px ${settingsTheme.colors.primary}20;
  }
`;

const SelectDropdown = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: ${settingsTheme.colors.background.main};
  border: 1px solid ${settingsTheme.colors.border};
  border-radius: ${settingsTheme.borderRadius.md};
  box-shadow: ${settingsTheme.shadows.lg};
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  display: ${props => (props.$isOpen ? 'block' : 'none')};
  margin-top: 4px;
`;

const SelectOption = styled.button`
  display: block;
  width: 100%;
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  text-align: left;
  background: none;
  border: none;
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.primary};
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.background.light};
  }

  &:focus {
    outline: none;
    background-color: ${settingsTheme.colors.primary}10;
  }
`;

const AddButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.xs};
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.primary};
  color: white;
  border: none;
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.primaryHover};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const SearchContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 400px;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  padding-left: 40px;
  border: 1px solid ${settingsTheme.colors.border};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  background-color: ${settingsTheme.colors.background.main};
  color: ${settingsTheme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${settingsTheme.colors.primary};
    box-shadow: 0 0 0 3px ${settingsTheme.colors.primary}20;
  }

  &::placeholder {
    color: ${settingsTheme.colors.text.tertiary};
  }
`;

const SearchIcon = styled(Search)`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: ${settingsTheme.colors.text.tertiary};
  width: 16px;
  height: 16px;
`;

// Table components
const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  box-shadow: ${settingsTheme.shadows.sm};
  border-radius: ${settingsTheme.borderRadius.md};
  overflow: hidden;
`;

const THead = styled.thead`
  background-color: ${settingsTheme.colors.background.light};
`;

const TH = styled.th`
  text-align: left;
  padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.base};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.secondary};
  border-bottom: 1px solid ${settingsTheme.colors.border};
`;

const TR = styled.tr`
  transition: ${settingsTheme.transitions.default};
  &:hover {
    background-color: ${settingsTheme.colors.background.light};
  }
`;

const TD = styled.td`
  padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.base};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.secondary};
  border-bottom: 1px solid ${settingsTheme.colors.border};
`;

const TableActionButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${settingsTheme.colors.text.tertiary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    background-color: ${settingsTheme.colors.background.lighter};
    color: ${settingsTheme.colors.primary};
  }

  &.delete {
    color: ${settingsTheme.colors.error.main};
  }
`;

const ActionButtonContainer = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const Avatar = styled.div<{ $imageUrl?: string }>`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  color: #4b5563;
  font-size: 0.875rem;
  background-size: cover;
  background-position: center;
  ${props =>
    props.$imageUrl &&
    `
    background-image: url(${props.$imageUrl});
    color: transparent;
  `}
`;

const UserCell = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const UserName = styled.div`
  font-weight: 500;
  color: #111827;
`;

const UserEmail = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
`;

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #e0f2fe;
  color: #0369a1;
`;

const NoResults = styled.div`
  padding: 1rem;
  text-align: center;
  color: #6b7280;
  font-size: 0.875rem;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: ${settingsTheme.spacing.xl};
`;

const ErrorContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};
  padding: ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.error}10;
  border: 1px solid ${settingsTheme.colors.error}30;
  border-radius: ${settingsTheme.borderRadius.md};
  color: ${settingsTheme.colors.error};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing.xl};
  text-align: center;
  color: ${settingsTheme.colors.text.secondary};
`;

const EmptyIcon = styled(Users)`
  width: 64px;
  height: 64px;
  margin-bottom: ${settingsTheme.spacing.md};
  opacity: 0.5;
`;

const EmptyTitle = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.lg};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0 0 ${settingsTheme.spacing.sm} 0;
`;

const EmptyDescription = styled.p`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.secondary};
  margin: 0;
  max-width: 400px;
`;

// Modal Styles
const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${settingsTheme.spacing.md};
`;

const ModalContent = styled.div`
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  padding: ${settingsTheme.spacing.xl};
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: ${settingsTheme.shadows.lg};
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const ModalTitle = styled.h2`
  font-size: ${settingsTheme.typography.fontSizes.xl};
  font-weight: ${settingsTheme.typography.fontWeights.bold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: ${settingsTheme.typography.fontSizes.xl};
  color: ${settingsTheme.colors.text.secondary};
  cursor: pointer;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.background.light};
    color: ${settingsTheme.colors.text.primary};
  }
`;

const ModalActions = styled.div`
  display: flex;
  gap: ${settingsTheme.spacing.md};
  justify-content: flex-end;
  margin-top: ${settingsTheme.spacing.lg};
`;

const ModalButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.lg};
  border: 1px solid
    ${props =>
      props.variant === 'primary' ? settingsTheme.colors.primary : settingsTheme.colors.border};
  background-color: ${props =>
    props.variant === 'primary'
      ? settingsTheme.colors.primary
      : settingsTheme.colors.background.light};
  color: ${props => (props.variant === 'primary' ? 'white' : settingsTheme.colors.text.primary)};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.xs};

  &:hover {
    background-color: ${props =>
      props.variant === 'primary'
        ? settingsTheme.colors.primaryHover
        : settingsTheme.colors.background.lighter};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

// Form Styles
const FormContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.md};
  margin: ${settingsTheme.spacing.md} 0;
`;

const FormRow = styled.div`
  display: flex;
  gap: ${settingsTheme.spacing.md};

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const FormField = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xs};
  flex: 1;
`;

const FormLabel = styled.label`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.primary};
`;

const FormInput = styled.input.withConfig({
  shouldForwardProp: prop => prop !== 'hasError',
})<{ hasError?: boolean }>`
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  border: 1px solid
    ${props => (props.hasError ? settingsTheme.colors.error : settingsTheme.colors.border)};
  border-radius: ${settingsTheme.borderRadius.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  background-color: ${settingsTheme.colors.background.main};
  color: ${settingsTheme.colors.text.primary};
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${props =>
      props.hasError ? settingsTheme.colors.error : settingsTheme.colors.primary};
    box-shadow: 0 0 0 3px
      ${props =>
        props.hasError ? settingsTheme.colors.error + '20' : settingsTheme.colors.primary + '20'};
  }

  &::placeholder {
    color: ${settingsTheme.colors.text.tertiary};
  }
`;

const FormError = styled.span`
  font-size: ${settingsTheme.typography.fontSizes.xs};
  color: ${settingsTheme.colors.error};
  margin-top: ${settingsTheme.spacing.xs};
`;

const RequiredIndicator = styled.span`
  color: ${settingsTheme.colors.error};
  margin-left: 2px;
`;

const OrganizationCheckboxContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.sm};
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid ${settingsTheme.colors.border};
  border-radius: ${settingsTheme.borderRadius.md};
  padding: ${settingsTheme.spacing.md};
`;

const CheckboxItem = styled.label`
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};
  cursor: pointer;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${settingsTheme.colors.background.light};
  }
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
  accent-color: ${settingsTheme.colors.primary};
`;

const CheckboxLabel = styled.span`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.primary};
`;

const ViewModeToggle = styled.div`
  display: flex;
  gap: ${settingsTheme.spacing.xs};
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.md};
  padding: ${settingsTheme.spacing.xs};
`;

const ViewModeButton = styled.button<{ $active: boolean }>`
  padding: ${settingsTheme.spacing.xs} ${settingsTheme.spacing.sm};
  border: none;
  border-radius: ${settingsTheme.borderRadius.sm};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: ${props => (props.$active ? settingsTheme.colors.primary : 'transparent')};
  color: ${props => (props.$active ? 'white' : settingsTheme.colors.text.secondary)};

  &:hover {
    background-color: ${props =>
      props.$active ? settingsTheme.colors.primaryHover : settingsTheme.colors.background.lighter};
  }
`;

interface Organization {
  id: number;
  name: string;
  description?: string;
}

interface Admin {
  id: number;
  userId: number;
  organizationId: number;
  assignedAt: string;
  isActive: boolean;
  user: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
    userRole: {
      id: number;
      name: string;
    };
  };
  assignedByUser?: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };
  organization: {
    id: number;
    name: string;
  };
}

export default function AdminPage() {
  const [mounted, setMounted] = useState(false);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingAdmins, setLoadingAdmins] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [addingAdmin, setAddingAdmin] = useState(false);
  const [selectedOrganizations, setSelectedOrganizations] = useState<number[]>([]);
  const [viewMode, setViewMode] = useState<'single' | 'all'>('single');
  const [allAdmins, setAllAdmins] = useState<Admin[]>([]);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Filter admins based on search term
  const currentAdmins = viewMode === 'all' ? allAdmins : admins;
  const filteredAdmins = currentAdmins.filter(
    admin =>
      admin.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      admin.user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      admin.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      admin.organization.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const fetchOrganizations = async () => {
    setLoading(true);
    setError(null);

    try {
      const token = getCookie('access_token');
      if (!token) {
        setError('Authentication required');
        return;
      }

      // Get user's owned organizations using the permissions API
      const response = await fetch('/api/v1/me', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch organizations');
      }

      const data = await response.json();
      if (data) {
        // Filter only owned organizations
        setOrganizations(data.organizations || []);

        // Auto-select first organization if available
        if (data.organizations.length > 0) {
          setSelectedOrganization(data.organizations[0]);
        }
      }
    } catch (err) {
      console.error('Error fetching organizations:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch organizations');
    } finally {
      setLoading(false);
    }
  };

  const fetchAdmins = async (organizationId: number) => {
    setLoadingAdmins(true);
    setError(null);

    try {
      const token = getCookie('access_token');
      if (!token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch(`/api/v1/organization-admin?organizationId=${organizationId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch admins');
      }

      const data = await response.json();
      setAdmins(data.admins || []);
    } catch (err) {
      console.error('Error fetching admins:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch admins');
    } finally {
      setLoadingAdmins(false);
    }
  };

  const fetchAllAdmins = async () => {
    setLoadingAdmins(true);
    setError(null);

    try {
      const token = getCookie('access_token');
      if (!token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch('/api/v1/organization-admin?all=true', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch all admins');
      }

      const data = await response.json();
      setAllAdmins(data.admins || []);
    } catch (err) {
      console.error('Error fetching all admins:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch all admins');
    } finally {
      setLoadingAdmins(false);
    }
  };

  const handleRemoveAdmin = async (adminId: number) => {
    if (!confirm('Are you sure you want to remove this admin?')) {
      return;
    }

    try {
      const token = getCookie('access_token');
      if (!token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch(`/api/v1/organization-admin?id=${adminId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove admin');
      }

      // Refresh the admins list based on current view mode
      if (viewMode === 'all') {
        fetchAllAdmins();
      } else if (selectedOrganization) {
        fetchAdmins(selectedOrganization.id);
      }
    } catch (err) {
      console.error('Error removing admin:', err);
      setError(err instanceof Error ? err.message : 'Failed to remove admin');
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Fetch organizations on mount
  useEffect(() => {
    fetchOrganizations();
  }, []);

  // Fetch admins when selected organization changes or view mode changes
  useEffect(() => {
    if (viewMode === 'single' && selectedOrganization) {
      fetchAdmins(selectedOrganization.id);
    } else if (viewMode === 'all') {
      fetchAllAdmins();
    }
  }, [selectedOrganization, viewMode]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required';
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }

    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    if (selectedOrganizations.length === 0) {
      errors.organizations = 'Please select at least one organization';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const isFormValid = () => {
    return (
      formData.firstName.trim() &&
      formData.lastName.trim() &&
      formData.email.trim() &&
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email) &&
      formData.password &&
      formData.password.length >= 6 &&
      formData.password === formData.confirmPassword &&
      selectedOrganizations.length > 0
    );
  };

  const handleOpenAddModal = () => {
    setShowAddModal(true);
    // Reset form data
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    });
    setFormErrors({});
    // Pre-select current organization if available
    if (selectedOrganization) {
      setSelectedOrganizations([selectedOrganization.id]);
    } else {
      setSelectedOrganizations([]);
    }
  };

  const handleCloseAddModal = () => {
    setShowAddModal(false);
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    });
    setFormErrors({});
    setSelectedOrganizations([]);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error for this field when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleOrganizationToggle = (organizationId: number) => {
    setSelectedOrganizations(prev => {
      if (prev.includes(organizationId)) {
        return prev.filter(id => id !== organizationId);
      } else {
        return [...prev, organizationId];
      }
    });

    // Clear organizations error when user selects at least one
    if (formErrors.organizations) {
      setFormErrors(prev => ({
        ...prev,
        organizations: '',
      }));
    }
  };

  const handleAddAdmin = async () => {
    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    setAddingAdmin(true);

    try {
      const token = getCookie('access_token');

      if (!token) {
        setError('Authentication required. Please login again.');
        alert('You need to login first. Redirecting to login page...');
        window.location.href = '/login';
        return;
      }

      const requestBody = {
        organizationIds: selectedOrganizations,
        createUser: {
          firstName: formData.firstName.trim(),
          lastName: formData.lastName.trim(),
          email: formData.email.trim(),
          phone: formData.phone.trim() || null,
          password: formData.password,
        },
      };

      const response = await fetch('/api/v1/organization-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.log('Error response:', errorData);
        throw new Error(errorData.error || 'Failed to create admin');
      }

      const responseData = await response.json();

      // Show success message
      if (responseData.errors && responseData.errors.length > 0) {
        setError(`Admin created with some issues: ${responseData.errors.join(', ')}`);
      } else {
        setError(null);
      }

      // Reset modal state
      handleCloseAddModal();

      // Refresh admins list based on current view mode
      if (viewMode === 'all') {
        fetchAllAdmins();
      } else if (selectedOrganization) {
        fetchAdmins(selectedOrganization.id);
      }
    } catch (err) {
      console.error('Error creating admin:', err);
      setError(err instanceof Error ? err.message : 'Failed to create admin');
    } finally {
      setAddingAdmin(false);
    }
  };

  // Prevent hydration errors
  if (!mounted) {
    return (
      <SettingsLayout>
        <AdminContainer>
          <LoadingContainer>
            <Loader2 size={24} className="animate-spin" />
          </LoadingContainer>
        </AdminContainer>
      </SettingsLayout>
    );
  }

  return (
    <SettingsLayout>
      <AdminContainer>
        <Header>
          <HeaderLeft>
            <Title>
              <Users size={24} />
              Organization Admins
            </Title>
          </HeaderLeft>
          <AddButton onClick={handleOpenAddModal} disabled={loading || organizations.length === 0}>
            <Plus size={16} />
            Add Admin
          </AddButton>
        </Header>

        {error && (
          <ErrorContainer>
            <AlertCircle size={16} />
            {error}
          </ErrorContainer>
        )}

        <FilterContainer>
          <ViewModeToggle>
            <ViewModeButton $active={viewMode === 'single'} onClick={() => setViewMode('single')}>
              Single Organization
            </ViewModeButton>
            <ViewModeButton $active={viewMode === 'all'} onClick={() => setViewMode('all')}>
              All Organizations
            </ViewModeButton>
          </ViewModeToggle>

          {viewMode === 'single' && (
            <OrganizationSelect>
              <SelectButton
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                disabled={loading || organizations.length === 0}
              >
                <span>
                  {selectedOrganization ? selectedOrganization.name : 'Select Organization'}
                </span>
                <ChevronDown size={16} />
              </SelectButton>
              <SelectDropdown $isOpen={isDropdownOpen}>
                {organizations.map(org => (
                  <SelectOption
                    key={org.id}
                    onClick={() => {
                      setSelectedOrganization(org);
                      setIsDropdownOpen(false);
                    }}
                  >
                    {org.name}
                  </SelectOption>
                ))}
              </SelectDropdown>
            </OrganizationSelect>
          )}

          <SearchContainer>
            <SearchIcon />
            <SearchInput
              type="text"
              placeholder={
                viewMode === 'all'
                  ? 'Search admins by name, email, or organization...'
                  : 'Search admins by name or email...'
              }
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
            />
          </SearchContainer>
        </FilterContainer>

        {loading ? (
          <LoadingContainer>
            <Loader2 size={24} className="animate-spin" />
          </LoadingContainer>
        ) : viewMode === 'single' && !selectedOrganization ? (
          <EmptyState>
            <EmptyIcon />
            <EmptyTitle>No Organization Selected</EmptyTitle>
            <EmptyDescription>
              Please select an organization from the dropdown above to view and manage admins.
            </EmptyDescription>
          </EmptyState>
        ) : loadingAdmins ? (
          <LoadingContainer>
            <Loader2 size={24} className="animate-spin" />
          </LoadingContainer>
        ) : filteredAdmins.length > 0 ? (
          <Table>
            <THead>
              <tr>
                <TH>Admin</TH>
                {viewMode === 'all' && <TH>Organization</TH>}
                <TH>Role</TH>
                <TH>Assigned Date</TH>
                <TH>Assigned By</TH>
                <TH>Actions</TH>
              </tr>
            </THead>
            <tbody>
              {filteredAdmins.map(admin => (
                <TR key={admin.id}>
                  <TD>
                    <UserCell>
                      <Avatar $imageUrl={admin.user.imageUrl}>
                        {getInitials(admin.user.firstName, admin.user.lastName)}
                      </Avatar>
                      <UserInfo>
                        <UserName>
                          {admin.user.firstName} {admin.user.lastName}
                        </UserName>
                        <UserEmail>{admin.user.email}</UserEmail>
                      </UserInfo>
                    </UserCell>
                  </TD>
                  {viewMode === 'all' && (
                    <TD>
                      <Badge>{admin.organization.name}</Badge>
                    </TD>
                  )}
                  <TD>
                    <Badge>Admin</Badge>
                  </TD>
                  <TD>{formatDate(admin.assignedAt)}</TD>
                  <TD>
                    {admin.assignedByUser
                      ? `${admin.assignedByUser.firstName} ${admin.assignedByUser.lastName}`
                      : 'System'}
                  </TD>
                  <TD>
                    <ActionButtonContainer>
                      <TableActionButton
                        className="delete"
                        onClick={() => handleRemoveAdmin(admin.id)}
                        title="Remove Admin"
                      >
                        <Trash size={16} />
                      </TableActionButton>
                    </ActionButtonContainer>
                  </TD>
                </TR>
              ))}
            </tbody>
          </Table>
        ) : (
          <Table>
            <THead>
              <tr>
                <TH>Admin</TH>
                {viewMode === 'all' && <TH>Organization</TH>}
                <TH>Role</TH>
                <TH>Assigned Date</TH>
                <TH>Assigned By</TH>
                <TH>Actions</TH>
              </tr>
            </THead>
            <tbody>
              <TR>
                <TD colSpan={viewMode === 'all' ? 6 : 5}>
                  <NoResults>
                    {searchTerm
                      ? `No admins match your search "${searchTerm}". Try a different search term.`
                      : viewMode === 'all'
                        ? 'You don\'t have any admins in your organizations yet. Click "Add Admin" to assign your first admin.'
                        : `${selectedOrganization?.name} doesn't have any admins yet. Click "Add Admin" to assign your first admin.`}
                  </NoResults>
                </TD>
              </TR>
            </tbody>
          </Table>
        )}
      </AdminContainer>

      {/* Create Admin Modal */}
      {showAddModal && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Create New Admin</ModalTitle>
              <CloseButton onClick={handleCloseAddModal}>
                <X size={20} />
              </CloseButton>
            </ModalHeader>

            <FormContainer>
              <FormRow>
                <FormField>
                  <FormLabel>
                    First Name <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="text"
                    placeholder="Enter first name"
                    value={formData.firstName}
                    onChange={e => handleInputChange('firstName', e.target.value)}
                    hasError={!!formErrors.firstName}
                  />
                  {formErrors.firstName && <FormError>{formErrors.firstName}</FormError>}
                </FormField>

                <FormField>
                  <FormLabel>
                    Last Name <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="text"
                    placeholder="Enter last name"
                    value={formData.lastName}
                    onChange={e => handleInputChange('lastName', e.target.value)}
                    hasError={!!formErrors.lastName}
                  />
                  {formErrors.lastName && <FormError>{formErrors.lastName}</FormError>}
                </FormField>
              </FormRow>

              <FormField>
                <FormLabel>
                  Email <RequiredIndicator>*</RequiredIndicator>
                </FormLabel>
                <FormInput
                  type="email"
                  placeholder="Enter email address"
                  value={formData.email}
                  onChange={e => handleInputChange('email', e.target.value)}
                  hasError={!!formErrors.email}
                />
                {formErrors.email && <FormError>{formErrors.email}</FormError>}
              </FormField>

              <FormField>
                <FormLabel>Phone Number</FormLabel>
                <FormInput
                  type="tel"
                  placeholder="Enter phone number (optional)"
                  value={formData.phone}
                  onChange={e => handleInputChange('phone', e.target.value)}
                />
              </FormField>

              <FormRow>
                <FormField>
                  <FormLabel>
                    Password <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="password"
                    placeholder="Enter password"
                    value={formData.password}
                    onChange={e => handleInputChange('password', e.target.value)}
                    hasError={!!formErrors.password}
                  />
                  {formErrors.password && <FormError>{formErrors.password}</FormError>}
                </FormField>

                <FormField>
                  <FormLabel>
                    Confirm Password <RequiredIndicator>*</RequiredIndicator>
                  </FormLabel>
                  <FormInput
                    type="password"
                    placeholder="Confirm password"
                    value={formData.confirmPassword}
                    onChange={e => handleInputChange('confirmPassword', e.target.value)}
                    hasError={!!formErrors.confirmPassword}
                  />
                  {formErrors.confirmPassword && (
                    <FormError>{formErrors.confirmPassword}</FormError>
                  )}
                </FormField>
              </FormRow>

              <FormField>
                <FormLabel>
                  Organizations <RequiredIndicator>*</RequiredIndicator>
                </FormLabel>
                <OrganizationCheckboxContainer>
                  {organizations.map(org => (
                    <CheckboxItem key={org.id}>
                      <Checkbox
                        type="checkbox"
                        checked={selectedOrganizations.includes(org.id)}
                        onChange={() => handleOrganizationToggle(org.id)}
                      />
                      <CheckboxLabel>{org.name}</CheckboxLabel>
                    </CheckboxItem>
                  ))}
                </OrganizationCheckboxContainer>
                {formErrors.organizations && <FormError>{formErrors.organizations}</FormError>}
              </FormField>
            </FormContainer>

            <ModalActions>
              <ModalButton onClick={handleCloseAddModal}>Cancel</ModalButton>
              <ModalButton
                variant="primary"
                onClick={handleAddAdmin}
                disabled={addingAdmin || !isFormValid()}
              >
                {addingAdmin ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus size={16} />
                    Create Admin
                  </>
                )}
              </ModalButton>
            </ModalActions>
          </ModalContent>
        </Modal>
      )}
    </SettingsLayout>
  );
}
