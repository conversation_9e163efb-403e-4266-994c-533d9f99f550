import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import bcrypt from 'bcrypt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

/**
 * PATCH API to update user information
 *
 * Body parameters:
 * - firstName: Optional. User's first name
 * - lastName: Optional. User's last name
 * - email: Optional. User's email
 * - phone: Optional. User's phone number
 * - password: Optional. New password
 */
export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const userId = parseInt(params.id);
    if (isNaN(userId)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Check if the authenticated user has permission to update this user
    // Only owners can update admin users, and users can update themselves
    const targetUser = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Permission check: owners can update anyone, admins can update members, users can update themselves
    if (auth.userId !== userId) {
      if (!auth.isOwner && !(auth.isAdmin && targetUser.userRole.isMember)) {
        return NextResponse.json({ error: 'You do not have permission to update this user' }, { status: 403 });
      }
    }

    const body = await request.json();
    const { firstName, lastName, email, phone, password } = body;

    // Prepare update data
    const updateData: any = {};

    if (firstName !== undefined) {
      if (!firstName.trim()) {
        return NextResponse.json({ error: 'First name cannot be empty' }, { status: 400 });
      }
      updateData.firstName = firstName.trim();
    }

    if (lastName !== undefined) {
      if (!lastName.trim()) {
        return NextResponse.json({ error: 'Last name cannot be empty' }, { status: 400 });
      }
      updateData.lastName = lastName.trim();
    }

    if (email !== undefined) {
      if (!email.trim()) {
        return NextResponse.json({ error: 'Email cannot be empty' }, { status: 400 });
      }

      // Validate email format
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
      }

      // Check if email is already taken by another user
      const existingUser = await prisma.user.findUnique({
        where: { email: email.trim() },
      });

      if (existingUser && existingUser.id !== userId) {
        return NextResponse.json({ error: 'Email is already taken' }, { status: 409 });
      }

      updateData.email = email.trim();
    }

    if (phone !== undefined) {
      updateData.phone = phone.trim() || null;
    }

    if (password !== undefined && password) {
      if (password.length < 6) {
        return NextResponse.json({ error: 'Password must be at least 6 characters' }, { status: 400 });
      }

      // Hash the new password
      const saltRounds = 10;
      updateData.passwordHash = await bcrypt.hash(password, saltRounds);
    }

    // Update the user
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        imageUrl: true,
        userRole: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'User updated successfully',
      user: updatedUser,
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
  }
}

/**
 * GET API to get user information by ID
 */
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const userId = parseInt(params.id);
    if (isNaN(userId)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Check if the authenticated user has permission to view this user
    const targetUser = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Permission check: owners can view anyone, admins can view members, users can view themselves
    if (auth.userId !== userId) {
      if (!auth.isOwner && !(auth.isAdmin && targetUser.userRole.isMember)) {
        return NextResponse.json({ error: 'You do not have permission to view this user' }, { status: 403 });
      }
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        imageUrl: true,
        userRole: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json({ user });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json({ error: 'Failed to fetch user' }, { status: 500 });
  }
}
