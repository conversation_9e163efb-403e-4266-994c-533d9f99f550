'use client';

import React, { useEffect } from 'react';
import styled from 'styled-components';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Users,
  Settings,
  ChevronRight,
  User,
  Building2,
  Trello,
  Trophy,
  Bell,
  MessageCircle,
  Shield,
} from 'lucide-react';
import useUserStore from '@/store/userStore';
import { USER_ROLES } from '@/constants/roles';
import { Tooltip } from '@/components/ui/tooltip';
import { useSidebar } from '../Sidebar';

const MenuContainer = styled.nav<{ $isCollapsed: boolean }>`
  flex: 1;
  padding: ${props => (props.$isCollapsed ? '0.75rem' : '0.75rem')};
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
`;

const MenuList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0rem;
  width: 100%;
`;

const MenuItem = styled.li<{ $isActive: boolean; $isCollapsed: boolean; $level?: number }>`
  position: relative;
  width: 100%;
  margin-left: ${props => (props.$level ? `${props.$level * 1.5}rem` : '0')};
  a,
  button {
    display: flex;
    align-items: center;
    width: 100%;
    padding: ${props => (props.$isCollapsed ? '0.75rem' : '0.75rem 0.75rem')};
    color: ${props => (props.$isActive ? '#ffffff' : '#6b7280')};
    background-color: ${props => (props.$isActive ? '#374151' : 'transparent')};
    text-decoration: none;
    font-weight: ${props => (props.$isActive ? '500' : '400')};
    font-size: 0.875rem;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
    border-radius: 0.75rem;
    justify-content: ${props => (props.$isCollapsed ? 'center' : 'flex-start')};

    &:hover {
      background-color: ${props => (props.$isActive ? '#374151' : '#f3f4f6')};
      color: ${props => (props.$isActive ? '#ffffff' : '#374151')};
      transform: translateY(-1px);
    }

    &:focus {
      outline: none;
    }

    &:focus-visible {
      outline: none;
    }

    &:active {
      transform: translateY(0);
    }
  }
`;

const MenuIcon = styled.span<{ $isCollapsed: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
  margin-right: ${props => (props.$isCollapsed ? '0' : '0.875rem')};
`;

const MenuLabel = styled.span<{ $isCollapsed: boolean }>`
  display: ${props => (props.$isCollapsed ? 'none' : 'block')};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-align: left;
`;

const ChevronIcon = styled.span<{ $isCollapsed: boolean; $isOpen: boolean }>`
  display: ${props => (props.$isCollapsed ? 'none' : 'flex')};
  align-items: center;
  justify-content: center;
  margin-left: 0.5rem;
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform: ${props => (props.$isOpen ? 'rotate(90deg)' : 'rotate(0deg)')};
`;

const SubMenuList = styled.ul<{ $isOpen: boolean; $isCollapsed: boolean }>`
  list-style: none;
  padding: ${props => (props.$isOpen ? '0.5rem 0 0 0' : '0')};
  margin: 0;
  display: ${props => (props.$isCollapsed ? 'none' : 'block')};
  max-height: ${props => (props.$isOpen ? '500px' : '0')};
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
`;

const SubMenuItem = styled.li<{ $isActive: boolean }>`
  position: relative;
  width: 100%;
  margin-bottom: 0.25rem;

  a {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    color: ${props => (props.$isActive ? '#ffffff' : '#9ca3af')};
    background-color: ${props => (props.$isActive ? '#4b5563' : 'transparent')};
    text-decoration: none;
    font-weight: ${props => (props.$isActive ? '500' : '400')};
    font-size: 0.8125rem;
    line-height: 1.25rem;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0.5rem;
    min-height: 2.5rem;

    &:hover {
      background-color: ${props => (props.$isActive ? '#4b5563' : '#f9fafb')};
      color: ${props => (props.$isActive ? '#ffffff' : '#374151')};
      transform: translateX(4px);
    }

    &:focus {
      outline: none;
    }

    &:focus-visible {
      outline: none;
    }

    &:active {
      transform: translateX(2px);
    }
  }
`;

const SubMenuIcon = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 1rem;
  width: 1rem;
  height: 1rem;
  margin-right: 0.75rem;
`;

const MenuSpacer = styled.span<{ $isCollapsed: boolean }>`
  display: ${props => (props.$isCollapsed ? 'none' : 'block')};
  width: 1.25rem;
  height: 1.25rem;
  margin-left: 0.5rem;
  flex-shrink: 0;
`;

interface SubMenuItem {
  path: string;
  label: string;
  icon?: React.ReactNode;
}

interface MenuItem {
  path: string;
  label: string;
  icon: React.ReactNode;
  subItems?: SubMenuItem[];
}

export default function SidebarMenu() {
  const pathname = usePathname();
  const { userData, fetchUserData } = useUserStore();
  const { isCollapsed, openSubmenus, toggleSubmenu, expandSidebar } = useSidebar();

  useEffect(() => {
    if (!userData) {
      fetchUserData();
    }
  }, [userData, fetchUserData]);

  // Define base menu items
  const menuItems: MenuItem[] = [
    {
      path: '/kanban',
      label: 'Kanban Board',
      icon: <Trello size={22} />,
    },
    {
      path: '/chat',
      label: 'Chat',
      icon: <MessageCircle size={22} />,
    },
    {
      path: '/ranking',
      label: 'Rankings',
      icon: <Trophy size={22} />,
    },
    {
      path: '/notification',
      label: 'Notifications',
      icon: <Bell size={22} />,
    },
    {
      path: '/settings',
      label: 'Settings',
      icon: <Settings size={22} />,
      subItems: [
        {
          path: '/settings',
          label: 'Profile',
          icon: <User size={18} />,
        },
        ...(userData && (userData.role?.name === USER_ROLES.OWNER || userData.role?.name === USER_ROLES.ADMIN)
          ? [
              {
                path: '/settings/organizations',
                label: 'Organizations',
                icon: <Building2 size={18} />,
              },
              {
                path: '/settings/admin',
                label: 'Admins',
                icon: <Shield size={18} />,
              },
              {
                path: '/settings/members',
                label: 'Members',
                icon: <Users size={18} />,
              },
            ]
          : []),
      ],
    },
  ];

  const handleSubmenuToggle = (path: string, hasSubItems: boolean) => {
    if (hasSubItems) {
      if (isCollapsed) {
        // If sidebar is collapsed, expand it first
        expandSidebar();
        // Then open the submenu
        toggleSubmenu(path);
      } else {
        // If sidebar is expanded, just toggle the submenu
        toggleSubmenu(path);
      }
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent, path: string, hasSubItems: boolean) => {
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        handleSubmenuToggle(path, hasSubItems);
        break;
      case 'ArrowRight':
        if (hasSubItems && !isCollapsed && !openSubmenus.has(path)) {
          event.preventDefault();
          toggleSubmenu(path);
        }
        break;
      case 'ArrowLeft':
        if (hasSubItems && !isCollapsed && openSubmenus.has(path)) {
          event.preventDefault();
          toggleSubmenu(path);
        }
        break;
    }
  };

  const renderMenuItem = (item: MenuItem, level: number = 0) => {
    const isActive = item.subItems ? pathname.startsWith(item.path) : pathname === item.path;
    const hasSubItems = item.subItems && item.subItems.length > 0;
    const isSubmenuOpen = openSubmenus.has(item.path);

    if (hasSubItems) {
      return (
        <div key={item.path}>
          <MenuItem $isActive={isActive} $isCollapsed={isCollapsed} $level={level} role="none">
            <button
              onClick={() => handleSubmenuToggle(item.path, hasSubItems)}
              onKeyDown={e => handleKeyDown(e, item.path, hasSubItems)}
              aria-expanded={isSubmenuOpen}
              aria-label={`${item.label}${hasSubItems ? ' submenu' : ''}`}
              aria-haspopup={hasSubItems ? 'menu' : undefined}
              role="menuitem"
            >
              <MenuIcon $isCollapsed={isCollapsed}>{item.icon}</MenuIcon>
              <MenuLabel $isCollapsed={isCollapsed}>{item.label}</MenuLabel>
              {hasSubItems && (
                <ChevronIcon $isCollapsed={isCollapsed} $isOpen={isSubmenuOpen}>
                  <ChevronRight size={18} />
                </ChevronIcon>
              )}
            </button>
          </MenuItem>

          {hasSubItems && (
            <SubMenuList
              $isOpen={isSubmenuOpen}
              $isCollapsed={isCollapsed}
              role="menu"
              aria-label={`${item.label} submenu`}
            >
              {item.subItems!.map(subItem => {
                const isSubActive = pathname === subItem.path;
                return (
                  <SubMenuItem key={subItem.path} $isActive={isSubActive} role="none">
                    <Link href={subItem.path} aria-label={subItem.label} role="menuitem">
                      <SubMenuIcon>{subItem.icon}</SubMenuIcon>
                      {subItem.label}
                    </Link>
                  </SubMenuItem>
                );
              })}
            </SubMenuList>
          )}
        </div>
      );
    }

    // Regular menu item without submenus
    const menuItemContent = (
      <MenuItem $isActive={isActive} $isCollapsed={isCollapsed} $level={level} role="none">
        <Link href={item.path} aria-label={item.label} role="menuitem">
          <MenuIcon $isCollapsed={isCollapsed}>{item.icon}</MenuIcon>
          <MenuLabel $isCollapsed={isCollapsed}>{item.label}</MenuLabel>
          <MenuSpacer $isCollapsed={isCollapsed} />
        </Link>
      </MenuItem>
    );

    // Show tooltip only when collapsed
    if (isCollapsed) {
      return (
        <Tooltip key={item.path} content={item.label} position="right">
          {menuItemContent}
        </Tooltip>
      );
    }

    return <div key={item.path}>{menuItemContent}</div>;
  };

  return (
    <MenuContainer $isCollapsed={isCollapsed} role="navigation" aria-label="Main navigation">
      <MenuList role="menu">{menuItems.map(item => renderMenuItem(item))}</MenuList>
    </MenuContainer>
  );
}
